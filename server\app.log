2025-06-09 19:28:36,016 - INFO - Starting application...
2025-06-09 19:28:36,029 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-09 19:28:36,029 - INFO - [33mPress CTRL+C to quit[0m
2025-06-09 19:28:46,728 - INFO - 127.0.0.1 - - [09/Jun/2025 19:28:46] "GET / HTTP/1.1" 200 -
2025-06-09 19:28:46,735 - INFO - 127.0.0.1 - - [09/Jun/2025 19:28:46] "GET /swaggerui/droid-sans.css HTTP/1.1" 200 -
2025-06-09 19:28:46,736 - INFO - 127.0.0.1 - - [09/Jun/2025 19:28:46] "GET /swaggerui/swagger-ui.css HTTP/1.1" 200 -
2025-06-09 19:28:46,737 - INFO - 127.0.0.1 - - [09/Jun/2025 19:28:46] "GET /swaggerui/swagger-ui-bundle.js HTTP/1.1" 200 -
2025-06-09 19:28:46,739 - INFO - 127.0.0.1 - - [09/Jun/2025 19:28:46] "GET /swaggerui/swagger-ui-standalone-preset.js HTTP/1.1" 200 -
2025-06-09 19:28:46,825 - INFO - 127.0.0.1 - - [09/Jun/2025 19:28:46] "GET /swagger.json HTTP/1.1" 200 -
2025-06-09 19:28:46,831 - INFO - 127.0.0.1 - - [09/Jun/2025 19:28:46] "GET /swaggerui/favicon-32x32.png HTTP/1.1" 200 -
2025-06-09 19:38:07,637 - INFO - Received status request
2025-06-09 19:38:07,637 - INFO - Current status: stopped, counts: {}
2025-06-09 19:38:07,638 - INFO - 127.0.0.1 - - [09/Jun/2025 19:38:07] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:38:09,653 - INFO - Received status request
2025-06-09 19:38:09,653 - INFO - Current status: stopped, counts: {}
2025-06-09 19:38:09,654 - INFO - 127.0.0.1 - - [09/Jun/2025 19:38:09] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:38:11,688 - INFO - Received status request
2025-06-09 19:38:11,688 - INFO - Current status: stopped, counts: {}
2025-06-09 19:38:11,689 - INFO - 127.0.0.1 - - [09/Jun/2025 19:38:11] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:38:13,699 - INFO - Received status request
2025-06-09 19:38:13,700 - INFO - Current status: stopped, counts: {}
2025-06-09 19:38:13,700 - INFO - 127.0.0.1 - - [09/Jun/2025 19:38:13] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:38:15,684 - INFO - Received status request
2025-06-09 19:38:15,685 - INFO - Current status: stopped, counts: {}
2025-06-09 19:38:15,686 - INFO - 127.0.0.1 - - [09/Jun/2025 19:38:15] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:38:17,686 - INFO - Received status request
2025-06-09 19:38:17,687 - INFO - Current status: stopped, counts: {}
2025-06-09 19:38:17,688 - INFO - 127.0.0.1 - - [09/Jun/2025 19:38:17] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:38:19,674 - INFO - Received status request
2025-06-09 19:38:19,674 - INFO - Current status: stopped, counts: {}
2025-06-09 19:38:19,675 - INFO - 127.0.0.1 - - [09/Jun/2025 19:38:19] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:38:21,721 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '480x480', 'fps': 15}
2025-06-09 19:38:21,721 - INFO - Starting stream with params - camera: 0, resolution: 480x480, fps: 15
2025-06-09 19:38:21,722 - INFO - StreamThread-20250609193821 - Starting stream processing
2025-06-09 19:38:21,722 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-09 19:38:21,723 - INFO - 127.0.0.1 - - [09/Jun/2025 19:38:21] "POST /api/start HTTP/1.1" 200 -
2025-06-09 19:38:22,909 - INFO - StreamThread-20250609193821 - Camera initialized
2025-06-09 19:38:22,913 - INFO - StreamThread-20250609193821 - Streamer initialized
2025-06-09 19:38:35,586 - ERROR - StreamThread-20250609193821 - Error in stream processing: [Errno 32] Broken pipe
Traceback (most recent call last):
  File "app.py", line 157, in process_stream
    streamer.process_frame(detected_frame)
  File "C:\Users\<USER>\Desktop\yolov8_count\server\utils\stream.py", line 124, in process_frame
    self.process.stdin.write(frame.tobytes())
BrokenPipeError: [Errno 32] Broken pipe
2025-06-09 19:38:35,845 - INFO - StreamThread-20250609193821 - Resources released, processed 2 frames in total
2025-06-09 19:38:40,009 - INFO - Received stop request
2025-06-09 19:38:40,010 - INFO - Stopping stream...
2025-06-09 19:38:40,010 - INFO - Stream thread joined successfully
2025-06-09 19:38:40,011 - INFO - Stream stopped successfully
2025-06-09 19:38:40,012 - INFO - 127.0.0.1 - - [09/Jun/2025 19:38:40] "GET /api/stop HTTP/1.1" 200 -
2025-06-09 19:38:41,044 - INFO - Received status request
2025-06-09 19:38:41,044 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:38:41,046 - INFO - 127.0.0.1 - - [09/Jun/2025 19:38:41] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:38:42,743 - INFO - Received status request
2025-06-09 19:38:42,743 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:38:42,744 - INFO - 127.0.0.1 - - [09/Jun/2025 19:38:42] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:38:43,779 - INFO - Received status request
2025-06-09 19:38:43,780 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:38:43,781 - INFO - 127.0.0.1 - - [09/Jun/2025 19:38:43] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:38:45,769 - INFO - Received status request
2025-06-09 19:38:45,769 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:38:45,770 - INFO - 127.0.0.1 - - [09/Jun/2025 19:38:45] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:38:47,761 - INFO - Received status request
2025-06-09 19:38:47,761 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:38:47,763 - INFO - 127.0.0.1 - - [09/Jun/2025 19:38:47] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:38:49,780 - INFO - Received status request
2025-06-09 19:38:49,780 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:38:49,781 - INFO - 127.0.0.1 - - [09/Jun/2025 19:38:49] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:38:51,746 - INFO - Received status request
2025-06-09 19:38:51,746 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:38:51,747 - INFO - 127.0.0.1 - - [09/Jun/2025 19:38:51] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:38:53,764 - INFO - Received status request
2025-06-09 19:38:53,764 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:38:53,765 - INFO - 127.0.0.1 - - [09/Jun/2025 19:38:53] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:38:55,755 - INFO - Received status request
2025-06-09 19:38:55,755 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:38:55,756 - INFO - 127.0.0.1 - - [09/Jun/2025 19:38:55] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:38:57,758 - INFO - Received status request
2025-06-09 19:38:57,758 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:38:57,760 - INFO - 127.0.0.1 - - [09/Jun/2025 19:38:57] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:38:59,761 - INFO - Received status request
2025-06-09 19:38:59,761 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:38:59,762 - INFO - 127.0.0.1 - - [09/Jun/2025 19:38:59] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:01,781 - INFO - Received status request
2025-06-09 19:39:01,781 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:01,782 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:01] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:03,755 - INFO - Received status request
2025-06-09 19:39:03,756 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:03,757 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:03] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:05,748 - INFO - Received status request
2025-06-09 19:39:05,748 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:05,749 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:05] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:07,769 - INFO - Received status request
2025-06-09 19:39:07,769 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:07,771 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:07] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:09,761 - INFO - Received status request
2025-06-09 19:39:09,762 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:09,763 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:09] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:11,769 - INFO - Received status request
2025-06-09 19:39:11,770 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:11,771 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:11] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:13,772 - INFO - Received status request
2025-06-09 19:39:13,772 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:13,774 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:13] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:15,748 - INFO - Received status request
2025-06-09 19:39:15,748 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:15,749 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:15] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:17,771 - INFO - Received status request
2025-06-09 19:39:17,771 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:17,773 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:17] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:19,793 - INFO - Received status request
2025-06-09 19:39:19,793 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:19,794 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:19] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:21,814 - INFO - Received status request
2025-06-09 19:39:21,815 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:21,816 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:21] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:23,804 - INFO - Received status request
2025-06-09 19:39:23,804 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:23,806 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:23] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:25,809 - INFO - Received status request
2025-06-09 19:39:25,810 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:25,811 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:25] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:27,817 - INFO - Received status request
2025-06-09 19:39:27,817 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:27,819 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:27] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:29,809 - INFO - Received status request
2025-06-09 19:39:29,809 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:29,811 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:29] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:31,817 - INFO - Received status request
2025-06-09 19:39:31,818 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:31,819 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:31] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:33,806 - INFO - Received status request
2025-06-09 19:39:33,807 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:33,808 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:33] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:35,814 - INFO - Received status request
2025-06-09 19:39:35,815 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:35,816 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:35] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:37,805 - INFO - Received status request
2025-06-09 19:39:37,806 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:37,807 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:37] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:39,811 - INFO - Received status request
2025-06-09 19:39:39,811 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:39,813 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:39] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:41,830 - INFO - Received status request
2025-06-09 19:39:41,830 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:41,832 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:41] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:43,822 - INFO - Received status request
2025-06-09 19:39:43,822 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:43,823 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:43] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:47,817 - INFO - Received status request
2025-06-09 19:39:47,817 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:47,818 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:47] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:49,845 - INFO - Received status request
2025-06-09 19:39:49,846 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:49,847 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:49] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:51,833 - INFO - Received status request
2025-06-09 19:39:51,834 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:51,835 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:51] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:39:53,839 - INFO - Received status request
2025-06-09 19:39:53,839 - INFO - Current status: stopped, counts: {'C': 1, 'LED': 3}
2025-06-09 19:39:53,840 - INFO - 127.0.0.1 - - [09/Jun/2025 19:39:53] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:41:09,882 - INFO - Starting application...
2025-06-09 19:41:09,891 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-09 19:41:09,892 - INFO - [33mPress CTRL+C to quit[0m
2025-06-09 19:41:17,120 - INFO - Received status request
2025-06-09 19:41:17,121 - INFO - Current status: stopped, counts: {}
2025-06-09 19:41:17,122 - INFO - 127.0.0.1 - - [09/Jun/2025 19:41:17] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:41:19,160 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '480x480', 'fps': 15}
2025-06-09 19:41:19,161 - INFO - Starting stream with params - camera: 0, resolution: 480x480, fps: 15
2025-06-09 19:41:19,162 - INFO - StreamThread-20250609194119 - Starting stream processing
2025-06-09 19:41:19,162 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-09 19:41:19,163 - INFO - 127.0.0.1 - - [09/Jun/2025 19:41:19] "POST /api/start HTTP/1.1" 200 -
2025-06-09 19:41:20,312 - INFO - StreamThread-20250609194119 - Camera initialized
2025-06-09 19:41:20,314 - INFO - StreamThread-20250609194119 - Streamer initialized
2025-06-09 19:41:28,520 - ERROR - StreamThread-20250609194119 - Error in stream processing: [Errno 32] Broken pipe
Traceback (most recent call last):
  File "app.py", line 157, in process_stream
    streamer.process_frame(detected_frame)
  File "C:\Users\<USER>\Desktop\yolov8_count\server\utils\stream.py", line 124, in process_frame
    self.process.stdin.write(frame.tobytes())
BrokenPipeError: [Errno 32] Broken pipe
2025-06-09 19:41:28,768 - INFO - StreamThread-20250609194119 - Resources released, processed 2 frames in total
2025-06-09 19:45:29,304 - INFO - Starting application...
2025-06-09 19:45:29,312 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-09 19:45:29,312 - INFO - [33mPress CTRL+C to quit[0m
2025-06-09 19:45:34,244 - INFO - Received status request
2025-06-09 19:45:34,244 - INFO - Current status: stopped, counts: {}
2025-06-09 19:45:34,245 - INFO - 127.0.0.1 - - [09/Jun/2025 19:45:34] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:45:36,280 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '480x480', 'fps': 15}
2025-06-09 19:45:36,280 - INFO - Starting stream with params - camera: 0, resolution: 480x480, fps: 15
2025-06-09 19:45:36,281 - INFO - StreamThread-20250609194536 - Starting stream processing
2025-06-09 19:45:36,281 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-09 19:45:36,281 - INFO - 127.0.0.1 - - [09/Jun/2025 19:45:36] "POST /api/start HTTP/1.1" 200 -
2025-06-09 19:45:37,362 - INFO - StreamThread-20250609194536 - Camera initialized
2025-06-09 19:45:37,363 - INFO - StreamThread-20250609194536 - Streamer initialized
2025-06-09 19:45:45,528 - ERROR - StreamThread-20250609194536 - Error in stream processing: [Errno 32] Broken pipe
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yolov8_count\server\app.py", line 157, in process_stream
    streamer.process_frame(detected_frame)
  File "C:\Users\<USER>\Desktop\yolov8_count\server\utils\stream.py", line 124, in process_frame
    self.process.stdin.write(frame.tobytes())
BrokenPipeError: [Errno 32] Broken pipe
2025-06-09 19:45:45,787 - INFO - StreamThread-20250609194536 - Resources released, processed 2 frames in total
2025-06-09 19:48:06,848 - INFO - Starting application...
2025-06-09 19:48:06,857 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-09 19:48:06,857 - INFO - [33mPress CTRL+C to quit[0m
2025-06-09 19:48:10,561 - INFO - Received status request
2025-06-09 19:48:10,562 - INFO - Current status: stopped, counts: {}
2025-06-09 19:48:10,562 - INFO - 127.0.0.1 - - [09/Jun/2025 19:48:10] "GET /api/status HTTP/1.1" 200 -
2025-06-09 19:48:12,601 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '480x480', 'fps': 15}
2025-06-09 19:48:12,601 - INFO - Starting stream with params - camera: 0, resolution: 480x480, fps: 15
2025-06-09 19:48:12,602 - INFO - StreamThread-20250609194812 - Starting stream processing
2025-06-09 19:48:12,602 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-09 19:48:12,602 - INFO - 127.0.0.1 - - [09/Jun/2025 19:48:12] "POST /api/start HTTP/1.1" 200 -
2025-06-09 19:48:13,682 - INFO - StreamThread-20250609194812 - Camera initialized
2025-06-09 19:48:13,683 - INFO - StreamThread-20250609194812 - Streamer initialized
2025-06-09 19:55:48,643 - INFO - Received stop request
2025-06-09 19:55:48,643 - INFO - Stopping stream...
2025-06-09 19:55:48,947 - INFO - StreamThread-20250609194812 - Resources released, processed 7379 frames in total
2025-06-09 19:55:48,947 - INFO - Stream thread joined successfully
2025-06-09 19:55:48,947 - INFO - Stream stopped successfully
2025-06-09 19:55:48,948 - INFO - 127.0.0.1 - - [09/Jun/2025 19:55:48] "GET /api/stop HTTP/1.1" 200 -
