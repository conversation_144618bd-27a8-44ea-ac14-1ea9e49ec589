import cv2
import numpy as np
import time
import datetime
import subprocess

# RTMP流地址
rtmp_url = "rtmp://10.42.82.17/live/stream1"

# 设置FFmpeg低延迟参数
ffmpeg_cmd = [
    'ffmpeg',
    '-fflags', 'nobuffer',          # 无缓冲区
    '-flags', 'low_delay',          # 低延迟标志
    '-probesize', '10K',            # 减少探测大小
    '-analyzeduration', '0',        # 不分析流持续时间
    '-i', rtmp_url,
    '-preset', 'ultrafast',         # 最快编码预设
    '-tune', 'zerolatency',         # 零延迟调优
    '-x264-params', 'keyint=30:min-keyint=30',  # 关键帧间隔
    '-bufsize', '800k',             # 固定缓冲区大小
    '-max_delay', '100000',         # 最大延迟100ms
    '-f', 'rawvideo',
    '-pix_fmt', 'bgr24',
    '-vcodec', 'rawvideo',
    '-an',                          # 禁用音频
    '-'
]

# 创建FFmpeg进程
process = subprocess.Popen(ffmpeg_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

# 视频参数
width, height = 640, 480
frame_size = width * height * 3  # 3 channels for BGR

print("正在以超低延迟模式拉取RTMP流，按'q'退出...")

# 延迟统计变量
delays = []
frame_count = 0
start_time = time.time()

# 用于NTP时间同步的变量（可选）
ntp_offset = 0  # 假设服务器和客户端时间已同步，或通过NTP校准

while True:
    # 记录接收前的本地时间（尽可能接近实际接收时间）
    local_time_before = time.time()

    # 从FFmpeg进程读取一帧
    raw_frame = process.stdout.read(frame_size)
    if not raw_frame or len(raw_frame) != frame_size:
        print("无法读取帧，可能流已断开！")
        break

    # 记录接收后的本地时间
    local_time_after = time.time()

    # 转换为numpy数组
    frame = np.frombuffer(raw_frame, dtype=np.uint8).reshape((height, width, 3))

    # 使用中间时间作为估计的接收时间
    local_time_received = (local_time_before + local_time_after) / 2

    # 假设服务器在画面中嵌入了时间戳（精确到毫秒）
    try:
        # 示例：从图像特定区域提取时间戳（需要根据实际调整）
        timestamp_region = frame[10:30, 10:200]  # 调整区域
        gray = cv2.cvtColor(timestamp_region, cv2.COLOR_BGR2GRAY)

        # 这里应该添加实际的时间戳文本识别代码
        # 示例使用假数据 - 实际应用中替换为OCR识别结果
        server_timestamp_str = f"{datetime.datetime.utcnow().strftime('%H:%M:%S.%f')[:-3]}"

        # 解析服务器时间戳
        server_time = datetime.datetime.strptime(server_timestamp_str, "%H:%M:%S.%f")
        server_time_seconds = (
                    server_time - server_time.replace(hour=0, minute=0, second=0, microsecond=0)).total_seconds()

        # 计算延迟（秒）
        current_time = datetime.datetime.utcnow()
        current_time_seconds = (
                    current_time - current_time.replace(hour=0, minute=0, second=0, microsecond=0)).total_seconds()
        delay = (current_time_seconds - server_time_seconds) * 1000  # 转换为毫秒

        # 应用NTP偏移（如果有）
        delay -= ntp_offset

        if delay >= 0:  # 忽略负延迟（时间同步问题）
            delays.append(delay)
            frame_count += 1

            # 显示延迟信息
            cv2.putText(frame, f"Curr: {delay:.2f}ms", (10, 30),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            cv2.putText(frame, f"Avg: {np.mean(delays):.2f}ms", (10, 60),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
            cv2.putText(frame, f"FPS: {frame_count / (time.time() - start_time):.1f}", (10, 90),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
    except Exception as e:
        print(f"时间戳处理错误: {e}")
    # ============ 新增的时间戳显示代码 ============
    # 在显示帧前添加当前本地时间戳
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    cv2.putText(frame, f"Local: {current_time}", (width - 300, 30),
                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 255), 1)
    # 如果成功提取了服务器时间戳，也显示出来
    if 'server_timestamp_str' in locals():
        cv2.putText(frame, f"Server: {server_timestamp_str}", (width - 300, 60),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 255), 1)
        # ============ 新增代码结束 ============
    # 显示帧
    cv2.imshow("RTMP Ultra Low-Latency Stream", frame)

    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

# 释放资源
process.terminate()
cv2.destroyAllWindows()

# 打印统计结果
if delays:
    print("\n===== 延迟统计 =====")
    print(f"平均延迟: {np.mean(delays):.2f} ms")
    print(f"最大延迟: {np.max(delays):.2f} ms")
    print(f"最小延迟: {np.min(delays):.2f} ms")
    print(f"帧率: {frame_count / (time.time() - start_time):.1f} fps")
else:
    print("未检测到有效延迟数据！")
