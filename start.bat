@echo off
chcp 65001 >nul
echo.
echo ================================================
echo 🚀 YOLOv8 低延时检测系统 - 一键启动
echo ================================================
echo.

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装或不在 PATH 中
    echo 请安装 Python 3.8+ 并添加到 PATH
    pause
    exit /b 1
)

REM 检查FFmpeg
ffmpeg -version >nul 2>&1
if errorlevel 1 (
    echo ❌ FFmpeg 未安装或不在 PATH 中
    echo 请安装 FFmpeg 并添加到 PATH
    pause
    exit /b 1
)

echo ✅ 环境检查通过
echo.
echo 🚀 启动系统...
echo.

python simple_start.py

pause
