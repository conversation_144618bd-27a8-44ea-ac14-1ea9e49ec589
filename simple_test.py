#!/usr/bin/env python3
"""
简化测试脚本 - 测试系统性能
"""

import cv2
import time
import numpy as np
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

def test_camera():
    """测试摄像头性能"""
    logger.info("🎥 测试摄像头性能...")
    
    try:
        cap = cv2.VideoCapture(0, cv2.CAP_DSHOW)
        if not cap.isOpened():
            logger.error("❌ 无法打开摄像头")
            return None
        
        # 应用优化设置
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
        cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('M', 'J', 'P', 'G'))
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 640)
        cap.set(cv2.CAP_PROP_FPS, 30)
        
        # 测试帧率
        frame_times = []
        start_time = time.time()
        
        for i in range(30):  # 测试30帧
            ret, frame = cap.read()
            if ret:
                frame_times.append(time.time())
            else:
                logger.error("❌ 读取帧失败")
                break
        
        cap.release()
        
        if len(frame_times) > 1:
            total_time = frame_times[-1] - frame_times[0]
            fps = (len(frame_times) - 1) / total_time
            avg_frame_time = total_time / (len(frame_times) - 1) * 1000
            
            logger.info(f"✅ 摄像头性能: {fps:.1f} FPS, 平均帧时间: {avg_frame_time:.1f}ms")
            return fps
        
    except Exception as e:
        logger.error(f"❌ 摄像头测试失败: {e}")
    
    return None

def test_detection():
    """测试检测性能"""
    logger.info("🔍 测试检测性能...")
    
    try:
        # 检查模型文件
        model_path = Path("server/models/best.pt")
        if not model_path.exists():
            logger.error("❌ 模型文件不存在")
            return None
        
        # 导入检测器
        import sys
        sys.path.append("server")
        from utils.detection import Detector
        
        # 初始化检测器
        detector = Detector(str(model_path))
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        
        # 预热
        detector.detect(test_image)
        
        # 测试检测时间
        detection_times = []
        for i in range(10):
            start_time = time.time()
            detected_frame, counts = detector.detect(test_image)
            end_time = time.time()
            
            detection_time = (end_time - start_time) * 1000
            detection_times.append(detection_time)
        
        avg_detection = sum(detection_times) / len(detection_times)
        min_detection = min(detection_times)
        max_detection = max(detection_times)
        
        logger.info(f"✅ 检测性能: 平均 {avg_detection:.1f}ms, 最快 {min_detection:.1f}ms, 最慢 {max_detection:.1f}ms")
        
        # 检查GPU使用
        import torch
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            logger.info(f"🚀 GPU加速: {gpu_name}")
        else:
            logger.info("⚠️  使用CPU检测 (建议使用GPU)")
        
        return avg_detection
        
    except Exception as e:
        logger.error(f"❌ 检测测试失败: {e}")
    
    return None

def test_ffmpeg():
    """测试FFmpeg性能"""
    logger.info("🎬 测试FFmpeg性能...")
    
    try:
        import subprocess
        
        # 测试FFmpeg编码速度
        cmd = [
            'ffmpeg', '-f', 'lavfi', '-i', 'testsrc=duration=2:size=640x640:rate=30',
            '-c:v', 'libx264', '-preset', 'ultrafast', '-tune', 'zerolatency',
            '-f', 'null', '-'
        ]
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        end_time = time.time()
        
        if result.returncode == 0:
            encode_time = end_time - start_time
            # 2秒视频，30fps = 60帧
            fps_encoded = 60 / encode_time
            logger.info(f"✅ FFmpeg性能: {fps_encoded:.1f} FPS 编码速度")
            return fps_encoded
        else:
            logger.error("❌ FFmpeg测试失败")
            
    except Exception as e:
        logger.error(f"❌ FFmpeg测试失败: {e}")
    
    return None

def estimate_total_latency(camera_fps, detection_time, ffmpeg_fps):
    """估算总延时"""
    logger.info("📊 估算系统延时...")
    
    # 各组件延时估算
    camera_latency = 1000 / camera_fps if camera_fps else 50  # 摄像头延时
    detection_latency = detection_time if detection_time else 100  # 检测延时
    encoding_latency = 1000 / ffmpeg_fps if ffmpeg_fps else 30  # 编码延时
    network_latency = 20  # 网络传输延时 (估算)
    display_latency = 16.7  # 显示延时 (60fps显示器)
    
    total_latency = camera_latency + detection_latency + encoding_latency + network_latency + display_latency
    
    logger.info(f"📈 延时分析:")
    logger.info(f"   摄像头: {camera_latency:.1f}ms")
    logger.info(f"   检测: {detection_latency:.1f}ms")
    logger.info(f"   编码: {encoding_latency:.1f}ms")
    logger.info(f"   网络: {network_latency:.1f}ms")
    logger.info(f"   显示: {display_latency:.1f}ms")
    logger.info(f"   总计: {total_latency:.1f}ms")
    
    # 性能评估
    if total_latency < 100:
        logger.info("🟢 优秀性能 - 超低延时")
    elif total_latency < 200:
        logger.info("🟡 良好性能 - 低延时")
    elif total_latency < 500:
        logger.info("🟠 中等性能 - 中等延时")
    else:
        logger.info("🔴 性能较差 - 高延时")
    
    return total_latency

def provide_suggestions(camera_fps, detection_time, ffmpeg_fps):
    """提供优化建议"""
    logger.info("💡 优化建议:")
    
    suggestions = []
    
    if camera_fps and camera_fps < 25:
        suggestions.append("摄像头FPS较低，检查USB连接或降低分辨率")
    
    if detection_time and detection_time > 50:
        suggestions.append("检测时间较长，建议使用GPU或降低模型复杂度")
    
    if ffmpeg_fps and ffmpeg_fps < 30:
        suggestions.append("FFmpeg编码较慢，检查CPU性能或使用硬件编码")
    
    if not suggestions:
        suggestions.append("系统性能良好，无需额外优化")
    
    for i, suggestion in enumerate(suggestions, 1):
        logger.info(f"   {i}. {suggestion}")

def main():
    """主函数"""
    print("=" * 50)
    print("🧪 YOLOv8 系统性能测试")
    print("=" * 50)
    
    # 测试各组件
    camera_fps = test_camera()
    detection_time = test_detection()
    ffmpeg_fps = test_ffmpeg()
    
    print("\n" + "=" * 50)
    print("📋 测试结果汇总")
    print("=" * 50)
    
    # 估算总延时
    total_latency = estimate_total_latency(camera_fps, detection_time, ffmpeg_fps)
    
    # 提供建议
    provide_suggestions(camera_fps, detection_time, ffmpeg_fps)
    
    print("\n" + "=" * 50)
    print("🎯 优化配置说明")
    print("=" * 50)
    print("本系统已应用以下低延时优化:")
    print("✅ Nginx chunk_size: 128 (默认4096)")
    print("✅ 缓冲时间: 100ms (默认1000ms)")
    print("✅ FFmpeg零延时调优")
    print("✅ 摄像头缓冲区: 1帧")
    print("✅ GPU半精度推理")
    print("✅ 智能跳帧策略")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
