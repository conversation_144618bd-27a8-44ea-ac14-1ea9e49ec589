2025-06-10 11:35:41,401 - INFO - Starting YOLOv8 detection system with project3 ultra-low-latency optimization...
2025-06-10 11:35:41,401 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-10 11:35:41,401 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-10 11:35:41,401 - INFO - Applied optimizations:
2025-06-10 11:35:41,405 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 11:35:41,405 - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 11:35:43,915 - INFO - Received status request
2025-06-10 11:35:43,915 - INFO - Current status: stopped, counts: {}
2025-06-10 11:35:43,916 - INFO - 127.0.0.1 - - [10/Jun/2025 11:35:43] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:35:45,948 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '480x480', 'fps': 15}
2025-06-10 11:35:45,948 - INFO - Starting stream with params - camera: 0, resolution: 480x480, fps: 15
2025-06-10 11:35:45,949 - INFO - StreamThread-20250610113545 - Starting stream processing
2025-06-10 11:35:45,949 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-10 11:35:45,949 - INFO - 127.0.0.1 - - [10/Jun/2025 11:35:45] "POST /api/start HTTP/1.1" 200 -
2025-06-10 11:35:47,927 - INFO - StreamThread-20250610113545 - Camera initialized
2025-06-10 11:35:47,928 - INFO - StreamThread-20250610113545 - Streamer initialized
2025-06-10 11:35:48,957 - ERROR - StreamThread-20250610113545 - Failed to read frame from camera
2025-06-10 11:35:48,961 - INFO - StreamThread-20250610113545 - Resources released, processed 1 frames in total
2025-06-10 11:36:17,965 - INFO - Received status request
2025-06-10 11:36:17,965 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:36:17,965 - INFO - 127.0.0.1 - - [10/Jun/2025 11:36:17] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:36:19,970 - INFO - Received status request
2025-06-10 11:36:19,970 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:36:19,971 - INFO - 127.0.0.1 - - [10/Jun/2025 11:36:19] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:36:21,937 - INFO - Received status request
2025-06-10 11:36:21,937 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:36:21,937 - INFO - 127.0.0.1 - - [10/Jun/2025 11:36:21] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:36:22,970 - INFO - Received status request
2025-06-10 11:36:22,970 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:36:22,971 - INFO - 127.0.0.1 - - [10/Jun/2025 11:36:22] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:36:24,982 - INFO - Received status request
2025-06-10 11:36:24,982 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:36:24,983 - INFO - 127.0.0.1 - - [10/Jun/2025 11:36:24] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:36:27,000 - INFO - Received status request
2025-06-10 11:36:27,000 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:36:27,001 - INFO - 127.0.0.1 - - [10/Jun/2025 11:36:27] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:36:28,976 - INFO - Received status request
2025-06-10 11:36:28,976 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:36:28,976 - INFO - 127.0.0.1 - - [10/Jun/2025 11:36:28] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:36:30,993 - INFO - Received status request
2025-06-10 11:36:30,993 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:36:30,994 - INFO - 127.0.0.1 - - [10/Jun/2025 11:36:30] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:36:32,981 - INFO - Received status request
2025-06-10 11:36:32,981 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:36:32,981 - INFO - 127.0.0.1 - - [10/Jun/2025 11:36:32] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:36:34,997 - INFO - Received status request
2025-06-10 11:36:34,997 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:36:34,997 - INFO - 127.0.0.1 - - [10/Jun/2025 11:36:34] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:36:36,987 - INFO - Received status request
2025-06-10 11:36:36,987 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:36:36,987 - INFO - 127.0.0.1 - - [10/Jun/2025 11:36:36] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:36:38,987 - INFO - Received status request
2025-06-10 11:36:38,987 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:36:38,988 - INFO - 127.0.0.1 - - [10/Jun/2025 11:36:38] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:36:40,993 - INFO - Received status request
2025-06-10 11:36:40,993 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:36:40,994 - INFO - 127.0.0.1 - - [10/Jun/2025 11:36:40] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:36:42,992 - INFO - Received status request
2025-06-10 11:36:42,992 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:36:42,992 - INFO - 127.0.0.1 - - [10/Jun/2025 11:36:42] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:36:45,004 - INFO - Received status request
2025-06-10 11:36:45,004 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:36:45,004 - INFO - 127.0.0.1 - - [10/Jun/2025 11:36:45] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:36:46,986 - INFO - Received status request
2025-06-10 11:36:46,986 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:36:46,987 - INFO - 127.0.0.1 - - [10/Jun/2025 11:36:46] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:36:48,987 - INFO - Received status request
2025-06-10 11:36:48,987 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:36:48,987 - INFO - 127.0.0.1 - - [10/Jun/2025 11:36:48] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:36:50,981 - INFO - Received status request
2025-06-10 11:36:50,981 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:36:50,982 - INFO - 127.0.0.1 - - [10/Jun/2025 11:36:50] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:36:52,981 - INFO - Received status request
2025-06-10 11:36:52,981 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:36:52,982 - INFO - 127.0.0.1 - - [10/Jun/2025 11:36:52] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:36:54,967 - INFO - Received status request
2025-06-10 11:36:54,967 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:36:54,967 - INFO - 127.0.0.1 - - [10/Jun/2025 11:36:54] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:36:56,985 - INFO - Received status request
2025-06-10 11:36:56,985 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:36:56,985 - INFO - 127.0.0.1 - - [10/Jun/2025 11:36:56] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:36:58,999 - INFO - Received status request
2025-06-10 11:36:58,999 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:36:58,999 - INFO - 127.0.0.1 - - [10/Jun/2025 11:36:58] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:37:00,995 - INFO - Received status request
2025-06-10 11:37:00,995 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:37:00,996 - INFO - 127.0.0.1 - - [10/Jun/2025 11:37:00] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:37:02,997 - INFO - Received status request
2025-06-10 11:37:02,997 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:37:02,998 - INFO - 127.0.0.1 - - [10/Jun/2025 11:37:02] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:37:05,012 - INFO - Received status request
2025-06-10 11:37:05,012 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:37:05,012 - INFO - 127.0.0.1 - - [10/Jun/2025 11:37:05] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:37:07,028 - INFO - Received status request
2025-06-10 11:37:07,028 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:37:07,029 - INFO - 127.0.0.1 - - [10/Jun/2025 11:37:07] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:37:09,013 - INFO - Received status request
2025-06-10 11:37:09,013 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:37:09,014 - INFO - 127.0.0.1 - - [10/Jun/2025 11:37:09] "GET /api/status HTTP/1.1" 200 -
2025-06-10 11:37:11,007 - INFO - Received status request
2025-06-10 11:37:11,007 - INFO - Current status: running, counts: {'C': 2, 'R': 8, 'LED': 4}
2025-06-10 11:37:11,007 - INFO - 127.0.0.1 - - [10/Jun/2025 11:37:11] "GET /api/status HTTP/1.1" 200 -
