import cv2

class CameraManager:
    def __init__(self):
        self.cameras = {}
    
    def get_camera(self, index, resolution='640x640', fps=30):
        if index in self.cameras:
            return self.cameras[index]

        # 解析分辨率
        width, height = map(int, resolution.split('x'))

        # 打开摄像头 - 使用DirectShow后端提高性能
        cap = cv2.VideoCapture(index, cv2.CAP_DSHOW)

        # 超低延时优化配置 (基于project3方案)
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 最小缓冲区，减少延迟
        cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('M', 'J', 'P', 'G'))  # MJPEG格式，快速处理

        # 设置分辨率和帧率
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
        cap.set(cv2.CAP_PROP_FPS, fps)

        # 额外的低延时优化
        cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.25)  # 禁用自动曝光减少处理时间
        cap.set(cv2.CAP_PROP_AUTOFOCUS, 0)  # 禁用自动对焦减少处理时间

        if not cap.isOpened():
            raise ValueError(f"无法打开摄像头索引 {index}")

        self.cameras[index] = cap
        return cap
    
    def release_all(self):
        for cap in self.cameras.values():
            cap.release()
        self.cameras.clear()
