import cv2

class CameraManager:
    def __init__(self):
        self.cameras = {}
    
    def get_camera(self, index, resolution='640x640', fps=30):
        if index in self.cameras:
            return self.cameras[index]

        # 解析分辨率
        width, height = map(int, resolution.split('x'))

        # 打开摄像头
        cap = cv2.VideoCapture(index, cv2.CAP_DSHOW)

        # 设置参数
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
        cap.set(cv2.CAP_PROP_FPS, fps)

        # 低延时优化
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 减少缓冲
        cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('M', 'J', 'P', 'G'))  # MJPEG格式

        if not cap.isOpened():
            raise ValueError(f"无法打开摄像头索引 {index}")

        self.cameras[index] = cap
        return cap
    
    def release_all(self):
        for cap in self.cameras.values():
            cap.release()
        self.cameras.clear()
