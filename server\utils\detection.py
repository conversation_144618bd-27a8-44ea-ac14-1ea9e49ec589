from ultralytics import YOLO
import cv2
import numpy as np
import torch

class Detector:
    def __init__(self, model_path):
        # 检查CUDA可用性
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"Using device: {self.device}")

        # 加载模型
        self.model = YOLO(model_path)

        self.last_counts = {}
        self.class_names = self.model.names

        # 预热模型
        dummy_frame = np.zeros((640, 640, 3), dtype=np.uint8)
        try:
            _ = self.model(dummy_frame, verbose=False)
            print("Model warmed up successfully")
        except Exception as e:
            print(f"Model warmup failed: {e}, but continuing...")

    def detect(self, frame):
        # 运行YOLOv8检测
        results = self.model(frame, verbose=False)

        # 解析结果
        boxes = results[0].boxes
        self.last_counts = {}

        # 绘制检测结果
        annotated_frame = results[0].plot()

        # 统计各类别数量
        if boxes is not None and len(boxes) > 0:
            for class_id in boxes.cls.unique():
                class_name = self.class_names[int(class_id)]
                count = len(boxes[boxes.cls == class_id])
                self.last_counts[class_name] = count

        return annotated_frame, self.last_counts

    def get_last_counts(self):
        return self.last_counts
