import sys
from PyQt5.QtWidgets import (QA<PERSON>lication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QPushButton, QLabel, QComboBox)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QImage, QPixmap
import cv2
import requests
import time
import datetime
import numpy as np


class VideoThread(QThread):
    change_pixmap = pyqtSignal(QImage)
    update_counts = pyqtSignal(dict)
    update_stats = pyqtSignal(str)  # 新增：用于显示延迟统计

    def __init__(self, rtmp_url, api_url):
        super().__init__()
        self.rtmp_url = rtmp_url
        self.api_url = api_url
        self.running = False
        self.cap = None

        # 延迟统计变量 (基于project3方案)
        self.delays = []
        self.frame_count = 0
        self.start_time = time.time()

    def run(self):
        self.running = True

        # 使用超低延迟配置打开RTMP流 (基于project3方案)
        self.cap = cv2.VideoCapture(self.rtmp_url)

        # 应用低延迟优化
        if self.cap.isOpened():
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 最小缓冲区
            self.cap.set(cv2.CAP_PROP_FPS, 30)  # 设置帧率

        while self.running and self.cap.isOpened():
            # 记录接收前的本地时间
            local_time_before = time.time()

            ret, frame = self.cap.read()
            if ret:
                # 记录接收后的本地时间
                local_time_after = time.time()

                # 计算接收延迟 (简化版本，实际项目中可以通过时间戳更精确计算)
                receive_delay = (local_time_after - local_time_before) * 1000  # 转换为毫秒

                self.frame_count += 1

                # 添加延迟统计
                if receive_delay >= 0:
                    self.delays.append(receive_delay)

                    # 每30帧更新一次统计信息
                    if self.frame_count % 30 == 0:
                        current_time = time.time()
                        fps = self.frame_count / (current_time - self.start_time)
                        avg_delay = np.mean(self.delays[-30:]) if self.delays else 0

                        stats_text = f"FPS: {fps:.1f} | 平均延迟: {avg_delay:.2f}ms | 帧数: {self.frame_count}"
                        self.update_stats.emit(stats_text)

                # 在帧上添加延迟信息 (基于project3方案)
                current_time_str = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
                cv2.putText(frame, f"Local: {current_time_str}", (10, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 255), 1)
                cv2.putText(frame, f"Delay: {receive_delay:.2f}ms", (10, 60),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

                # 转换并发送图像
                rgb_image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                h, w, ch = rgb_image.shape
                bytes_per_line = ch * w
                qt_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888)
                self.change_pixmap.emit(qt_image)
            else:
                # 如果读取失败，短暂等待后重试
                time.sleep(0.01)

        if self.cap:
            self.cap.release()

    def get_counts(self):
        try:
            response = requests.get(f"{self.api_url}/status")
            if response.status_code == 200:
                data = response.json()
                self.update_counts.emit(data.get('counts', {}))
        except Exception as e:
            print(f"获取计数错误: {e}")

    def stop(self):
        self.running = False
        self.wait()


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("电子元器件计数系统")
        self.setGeometry(100, 100, 800, 600)

        # 配置
        self.rtmp_url = "rtmp://localhost/live/stream"
        self.api_url = "http://localhost:5000/api"

        # 初始化UI
        self.init_ui()

        # 视频线程
        self.video_thread = None

        # 状态检查定时器
        self.status_timer = QTimer(self)
        self.status_timer.timeout.connect(self.check_server_status)
        self.status_timer.start(1000)  # 每秒检查一次

    def init_ui(self):
        # 主部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)

        # 视频显示区域
        self.video_label = QLabel()
        self.video_label.setAlignment(Qt.AlignCenter)
        self.video_label.setMinimumSize(640, 640)
        self.video_label.setStyleSheet("background-color: black;")
        main_layout.addWidget(self.video_label)

        # 控制面板
        control_panel = QHBoxLayout()

        # 摄像头选择
        self.camera_combo = QComboBox()
        self.camera_combo.addItems(["摄像头0", "摄像头1", "摄像头2"])
        control_panel.addWidget(QLabel("选择摄像头:"))
        control_panel.addWidget(self.camera_combo)

        # 分辨率选择
        self.resolution_combo = QComboBox()
        self.resolution_combo.addItems(["480x480", "640x640", "800x600"])
        control_panel.addWidget(QLabel("分辨率:"))
        control_panel.addWidget(self.resolution_combo)

        # 帧率选择
        self.fps_combo = QComboBox()
        self.fps_combo.addItems(["15", "24", "30", "60"])
        control_panel.addWidget(QLabel("帧率:"))
        control_panel.addWidget(self.fps_combo)

        main_layout.addLayout(control_panel)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 开始按钮
        self.start_btn = QPushButton("开始检测")
        self.start_btn.setStyleSheet("background-color: #4CAF50; color: white;")
        self.start_btn.clicked.connect(self.start_detection)
        button_layout.addWidget(self.start_btn)

        # 停止按钮
        self.stop_btn = QPushButton("停止检测")
        self.stop_btn.setStyleSheet("background-color: #f44336; color: white;")
        self.stop_btn.clicked.connect(self.stop_detection)
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.stop_btn)

        main_layout.addLayout(button_layout)

        # 计数显示区域
        self.count_layout = QHBoxLayout()
        self.count_labels = {}

        # 初始化计数标签
        for component in ["电阻", "电容", "LED"]:  # 根据实际模型类别修改
            label = QLabel(f"{component}: 0")
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("font-size: 16px; font-weight: bold;")
            self.count_layout.addWidget(label)
            self.count_labels[component] = label

        main_layout.addLayout(self.count_layout)

        # 延迟统计显示区域 (基于project3方案)
        self.stats_label = QLabel("延迟统计: 等待数据...")
        self.stats_label.setAlignment(Qt.AlignCenter)
        self.stats_label.setStyleSheet("font-size: 14px; color: blue; font-weight: bold;")
        main_layout.addWidget(self.stats_label)

        # 状态栏
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("准备就绪 - 已应用project3超低延迟优化")

    def start_detection(self):
        # 获取参数
        camera_index = self.camera_combo.currentIndex()
        resolution = self.resolution_combo.currentText()
        fps = int(self.fps_combo.currentText())

        # 发送API请求
        try:
            response = requests.post(
                f"{self.api_url}/start",
                json={
                    "camera_index": camera_index,
                    "resolution": resolution,
                    "fps": fps
                }
            )

            if response.status_code == 200:
                data = response.json()
                self.status_bar.showMessage(f"已启动 - RTMP: {data.get('rtmp_url', '')}")

                # 启动视频线程
                if self.video_thread is None:
                    self.video_thread = VideoThread(self.rtmp_url, self.api_url)
                    self.video_thread.change_pixmap.connect(self.set_image)
                    self.video_thread.update_counts.connect(self.update_count_labels)
                    self.video_thread.update_stats.connect(self.update_stats_display)  # 连接统计信号
                    self.video_thread.start()

                # 更新按钮状态
                self.start_btn.setEnabled(False)
                self.stop_btn.setEnabled(True)
            else:
                self.status_bar.showMessage(f"启动失败: {response.text}")

        except Exception as e:
            self.status_bar.showMessage(f"连接服务器失败: {str(e)}")

    def stop_detection(self):
        try:
            response = requests.get(f"{self.api_url}/stop")

            if response.status_code == 200:
                self.status_bar.showMessage("已停止检测")

                # 停止视频线程
                if self.video_thread is not None:
                    self.video_thread.stop()
                    self.video_thread = None

                # 更新按钮状态
                self.start_btn.setEnabled(True)
                self.stop_btn.setEnabled(False)

                # 清空视频显示
                self.video_label.clear()
                self.video_label.setStyleSheet("background-color: black;")

            else:
                self.status_bar.showMessage(f"停止失败: {response.text}")

        except Exception as e:
            self.status_bar.showMessage(f"连接服务器失败: {str(e)}")

    def set_image(self, image):
        self.video_label.setPixmap(QPixmap.fromImage(image).scaled(
            self.video_label.width(),
            self.video_label.height(),
            Qt.KeepAspectRatio
        ))

    def update_count_labels(self, counts):
        for component, count in counts.items():
            if component in self.count_labels:
                self.count_labels[component].setText(f"{component}: {count}")
            else:
                # 动态添加新组件计数
                label = QLabel(f"{component}: {count}")
                label.setAlignment(Qt.AlignCenter)
                label.setStyleSheet("font-size: 16px; font-weight: bold;")
                self.count_layout.addWidget(label)
                self.count_labels[component] = label

    def update_stats_display(self, stats_text):
        """更新延迟统计显示 (基于project3方案)"""
        self.stats_label.setText(f"延迟统计: {stats_text}")

    def check_server_status(self):
        if self.video_thread and self.video_thread.isRunning():
            return

        try:
            response = requests.get(f"{self.api_url}/status", timeout=1)
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'running':
                    # 自动重新连接
                    if not self.video_thread:
                        self.video_thread = VideoThread(self.rtmp_url, self.api_url)
                        self.video_thread.change_pixmap.connect(self.set_image)
                        self.video_thread.update_counts.connect(self.update_count_labels)
                        self.video_thread.update_stats.connect(self.update_stats_display)  # 连接统计信号
                        self.video_thread.start()
                        self.start_btn.setEnabled(False)
                        self.stop_btn.setEnabled(True)
                        self.status_bar.showMessage("已重新连接视频流 - project3超低延迟模式")
        except:
            pass

    def closeEvent(self, event):
        if self.video_thread is not None:
            self.video_thread.stop()
        super().closeEvent(event)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())
